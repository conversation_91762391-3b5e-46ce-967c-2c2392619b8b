import BaseModel from './BaseModel.js';
import { Enums, Fields, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';

/**
 * Model User - Quản lý thông tin người dùng
 * Chỉ chứa định nghĩa cấu trúc, logic query được tách ra service
 */
export default class UserModel extends BaseModel {
  static get tableName() {
    return 'user';
  }

  /**
   * Định nghĩa các field của bảng user
   */
  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      user_name: Fields.userName(),
      email: Fields.email(),
      phone: Fields.phone(),
      status: Fields.status(),
      ip: Fields.ip(),
      create_time: Fields.createTime(),
    });
  }

  /**
   * <PERSON><PERSON>u hình tùy chọn
   */
  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'] },
        { fields: ['user_name'], unique: true },
        { fields: ['email'] },
        { fields: ['status'] },
        { fields: ['ip'] },
        { fields: ['create_time'] },
        { fields: ['user_id', 'status'] },
        { fields: ['create_time', 'status'] },
      ],
    });
  }

  /**
   * Lấy enum status
   */
  static getStatusEnum() {
    return Enums.USER_STATUS;
  }

  /**
   * Transform dữ liệu user
   */
  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      ip: Utils.formatIP(row.ip),
      email: Utils.sanitizeString(row.email),
      phone: Utils.sanitizeString(row.phone),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  /**
   * Validate dữ liệu user
   */
  static validate(data) {
    super.validate(data);

    if (data.email && !Utils.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (data.phone && !Utils.isValidPhone(data.phone)) {
      throw new Error('Invalid phone format');
    }

    return true;
  }

  // === QUERY HELPERS (Static methods like MySQL models) ===

  /**
   * Tìm user active
   */
  static async findActive(options = {}) {
    return await MySQL2Service.findActive(this.tableName, options, this.transform);
  }

  /**
   * Tìm user theo IP
   */
  static async findByIP(ip, options = {}) {
    return await MySQL2Service.findByIP(this.tableName, ip, options, this.transform);
  }

  /**
   * Tìm user hôm nay
   */
  static async findToday(options = {}) {
    return await MySQL2Service.findToday(this.tableName, options, this.transform);
  }

  /**
   * Tìm user tháng này
   */
  static async findThisMonth(options = {}) {
    return await MySQL2Service.findThisMonth(this.tableName, options, this.transform);
  }

  /**
   * Tìm user trong khoảng thời gian
   */
  static async findByTimeRange(startTime, endTime, options = {}) {
    return await MySQL2Service.findByTimeRange(this.tableName, startTime, endTime, options, this.transform);
  }

  /**
   * Lấy thông tin user theo ID
   */
  static async findByUserId(userId) {
    return await MySQL2Service.findById(this.tableName, 'user_id', userId, this.transform);
  }

  /**
   * Đếm user theo status
   */
  static async countByStatus(status) {
    return await MySQL2Service.countDocuments(this.tableName, { status });
  }

  /**
   * Đếm tổng số user
   */
  static async estimatedDocumentCount() {
    return await MySQL2Service.estimatedDocumentCount(this.tableName);
  }

  /**
   * Đếm user theo điều kiện
   */
  static async countDocuments(conditions = {}) {
    return await MySQL2Service.countDocuments(this.tableName, conditions);
  }

  /**
   * Lấy danh sách IP duy nhất
   */
  static async distinct(field, conditions = {}) {
    return await MySQL2Service.distinct(this.tableName, field, conditions);
  }
}
