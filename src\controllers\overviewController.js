import { errorResponse } from '../utils/apiResponseUltil.js';
import {
  getOverview as getOverviewService,
  getOverviewByDate as getOverviewByDateService,
  getDailyStatistics as getDailyStatisticsService,
  getRevenueStatistics as getRevenueStatisticsService,
} from '../services/statisticService.js';
import logger from '../config/logger.js';

/**
 * Controller for getting overview statistics
 * Handles HTTP requests and responses, delegates data fetching to service
 */
export const getOverview = async (req, res) => {
  try {
    const stats = await getOverviewService();
    return res.status(200).json(stats);
  } catch (error) {
    logger.error('Error getting overview stats:', error);
    return res.status(500).json({ error: 'Failed to get overview statistics' });
  }
};

/**
 * Controller for getting overview statistics by date range
 * POST /api/overview/by-date
 */
export const getOverviewByDate = async (req, res) => {
  try {
    console.log("req.body gửi lên" , req.body);
    const { start_date, end_date, type, time_period } = req.body;

    // Validate required parameters
    if (!start_date || !end_date || !type) {
      return res
        .status(400)
        .json(errorResponse('Missing required parameters: start_date, end_date, type'));
    }

    // Validate type parameter
    if (![1, 2, 3, 4].includes(type)) {
      return res.status(400).json(errorResponse('Invalid type. Must be 1, 2, 3, or 4'));
    }

    const result = await getOverviewByDateService({
      start_date,
      end_date,
      type,
      time_period,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting overview by date:', error);
    return res.status(500).json(errorResponse('Failed to get overview statistics by date'));
  }
};

/**
 * Controller for getting daily statistics
 * POST /api/statistics/daily
 */
export const getDailyStatistics = async (req, res) => {
  try {
    const { start_date, end_date, type } = req.body;

    // Validate required parameters
    if (!start_date || !end_date) {
      return res
        .status(400)
        .json(errorResponse('Missing required parameters: start_date, end_date'));
    }

    // Validate type parameter if provided
    if (type !== null && type !== undefined && ![1, 2].includes(type)) {
      return res.status(400).json(errorResponse('Invalid type. Must be 1, 2, or null'));
    }

    const result = await getDailyStatisticsService({
      start_date,
      end_date,
      type,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting daily statistics:', error);
    return res.status(500).json(errorResponse('Failed to get daily statistics'));
  }
};

/**
 * Controller for getting revenue statistics
 * POST /api/statistics/revenue
 */
export const getRevenueStatistics = async (req, res) => {
  try {
    const { start_date, end_date } = req.body;

    // Validate required parameters
    if (!start_date || !end_date) {
      return res
        .status(400)
        .json(errorResponse('Missing required parameters: start_date, end_date'));
    }

    const result = await getRevenueStatisticsService({
      start_date,
      end_date,
    });

    return res.status(200).json(result);
  } catch (error) {
    logger.error('Error getting revenue statistics:', error);
    return res.status(500).json(errorResponse('Failed to get revenue statistics'));
  }
};
