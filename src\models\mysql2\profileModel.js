import BaseModel from './BaseModel.js';
import { Enums, Fields, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';

/**
 * Model Profile - Qu<PERSON>n lý thông tin hồ sơ người dùng
 * Chỉ chứa định nghĩa cấu trúc, logic query được tách ra service
 * Tuân theo pattern của MySQL models
 */
export default class ProfileModel extends BaseModel {
  static get tableName() {
    return 'profile';
  }

  /**
   * Định nghĩa các field của bảng profile
   */
  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      first_name: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'first_name',
        comment: 'Tên',
      },
      last_name: {
        type: 'VARCHAR(100)',
        allowNull: true,
        field: 'last_name',
        comment: 'H<PERSON>',
      },
      birth_date: {
        type: 'DATE',
        allowNull: true,
        field: 'birth_date',
        comment: '<PERSON><PERSON><PERSON> sinh',
      },
      gender: {
        type: 'TINYINT',
        allowNull: false,
        defaultValue: 0,
        comment: 'Giới tính',
      },
      avatar: {
        type: 'VARCHAR(255)',
        allowNull: true,
        comment: 'Avatar URL',
      },
      bio: {
        type: 'TEXT',
        allowNull: true,
        comment: 'Tiểu sử',
      },
      status: Fields.status(),
    });
  }

  /**
   * Cấu hình tùy chọn
   */
  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'], unique: true },
        { fields: ['status'] },
        { fields: ['gender'] },
        { fields: ['birth_date'] },
        { fields: ['user_id', 'status'] },
      ],
    });
  }

  /**
   * Lấy enum status
   */
  static getStatusEnum() {
    return Enums.PROFILE_STATUS;
  }

  /**
   * Lấy enum gender
   */
  static getGenderEnum() {
    return Enums.GENDER;
  }

  /**
   * Transform dữ liệu profile
   */
  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      first_name: Utils.sanitizeString(row.first_name),
      last_name: Utils.sanitizeString(row.last_name),
      bio: Utils.sanitizeString(row.bio),
      full_name: row.first_name && row.last_name ?
        `${row.first_name} ${row.last_name}` : null,
      age: row.birth_date ? ProfileModel.calculateAge(row.birth_date) : null,
    };
  }

  /**
   * Tính tuổi từ ngày sinh
   */
  static calculateAge(birthDate) {
    if (!birthDate) return null;

    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  }

  /**
   * Validate dữ liệu profile
   */
  static validate(data) {
    super.validate(data);

    if (data.birth_date) {
      const birthDate = new Date(data.birth_date);
      const today = new Date();

      if (birthDate >= today) {
        throw new Error('Birth date must be in the past');
      }

      const age = ProfileModel.calculateAge(data.birth_date);
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120');
      }
    }

    if (data.gender !== undefined && !Object.values(Enums.GENDER).includes(data.gender)) {
      throw new Error('Invalid gender value');
    }

    return true;
  }

  // === QUERY HELPERS (Static methods like MySQL models) ===

  /**
   * Lấy profile theo user ID
   */
  static async findByUserId(userId) {
    return await MySQL2Service.findById(this.tableName, 'user_id', userId, this.transform);
  }

  /**
   * Tìm profile complete
   */
  static async findComplete(options = {}) {
    return await MySQL2Service.find(this.tableName, { status: Enums.PROFILE_STATUS.COMPLETE }, options, this.transform);
  }

  /**
   * Tìm profile verified
   */
  static async findVerified(options = {}) {
    return await MySQL2Service.find(this.tableName, { status: Enums.PROFILE_STATUS.VERIFIED }, options, this.transform);
  }

  /**
   * Đếm tổng số profile
   */
  static async estimatedDocumentCount() {
    return await MySQL2Service.estimatedDocumentCount(this.tableName);
  }

  /**
   * Đếm profile theo điều kiện
   */
  static async countDocuments(conditions = {}) {
    return await MySQL2Service.countDocuments(this.tableName, conditions);
  }

  /**
   * Đếm profile complete
   */
  static async countComplete() {
    return await MySQL2Service.countDocuments(this.tableName, { status: Enums.PROFILE_STATUS.COMPLETE });
  }

  /**
   * Đếm profile verified
   */
  static async countVerified() {
    return await MySQL2Service.countDocuments(this.tableName, { status: Enums.PROFILE_STATUS.VERIFIED });
  }

  /**
   * Đếm profile theo status
   */
  static async countByStatus(status) {
    return await MySQL2Service.countDocuments(this.tableName, { status });
  }
}
