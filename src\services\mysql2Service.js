import { mysql2Pool } from '../config/mysql2.js';
import logger from '../config/logger.js';
import { Queries, Utils } from '../models/mysql2/helpers.js';

class MySQL2Service {
  // Thực thi query đơn giản với nhi<PERSON>u tùy chọn
  static async query(sql, params = [], options = {}) {
    try {
      const { debug = false } = options;

      if (debug) {
        logger.debug(`🔍 MySQL2 Query: ${sql}`, { params });
      }

      const [rows] = await mysql2Pool.execute(sql, params);

      if (debug) {
        logger.debug(`✅ MySQL2 Query Result: ${rows.length} rows`);
      }

      return rows;
    } catch (error) {
      logger.error('❌ MySQL2 Query Error:', {
        sql,
        params,
        error: error.message,
        code: error.code,
      });
      throw error;
    }
  }

  // L<PERSON>y một bản ghi với validation
  static async getOne(sql, params = [], options = {}) {
    try {
      const rows = await this.query(sql, params, options);
      const result = rows[0] || null;

      if (options.required && !result) {
        throw new Error(`Record not found for query: ${sql}`);
      }

      return result;
    } catch (error) {
      logger.error('❌ MySQL2 GetOne Error:', error);
      throw error;
    }
  }

  // Đếm số bản ghi
  static async count(table, where = '1', params = [], options = {}) {
    try {
      const { distinct = null, alias = 'total' } = options;

      let sql = `SELECT COUNT(${distinct ? `DISTINCT ${distinct}` : '*'}) as ${alias} FROM ${table}`;

      if (where && where !== '1') {
        sql += ` WHERE ${where}`;
      }

      const result = await this.getOne(sql, params, options);
      if (!result) return 0;

      // Safe access to result properties
      return Number(result[alias]) || 0;
    } catch (error) {
      logger.error('❌ MySQL2 Count Error:', error);
      throw error;
    }
  }

  // Lấy danh sách IP duy nhất
  static async distinctIPs(startTime = null, endTime = null) {
    try {
      let sql = 'SELECT DISTINCT ip FROM user WHERE ip IS NOT NULL AND ip != ""';
      const params = [];

      if (startTime && endTime) {
        sql += ' AND create_time BETWEEN ? AND ?';
        params.push(startTime, endTime);
      }

      const rows = await this.query(sql, params);
      return rows.map(row => row.ip);
    } catch (error) {
      logger.error('❌ MySQL2 Distinct IPs Error:', error);
      throw error;
    }
  }

  // Kiểm tra kết nối
  static async testConnection() {
    try {
      const connection = await mysql2Pool.getConnection();
      connection.release();
      return true;
    } catch (error) {
      logger.error('❌ MySQL2 Connection Test Failed:', error);
      return false;
    }
  }

  // === USER QUERY METHODS (Moved from userQueryService.js) ===

  /**
   * Build WHERE clause từ conditions
   * @param {Object} conditions - Điều kiện query
   * @returns {Object} { whereClause, params }
   */
  static buildWhereClause(conditions) {
    if (!conditions || Object.keys(conditions).length === 0) {
      return { whereClause: '1', params: [] };
    }

    const clauses = [];
    const params = [];

    for (const [key, value] of Object.entries(conditions)) {
      if (key === 'createTime' && typeof value === 'object') {
        // Xử lý range query cho createTime
        if (value.$gte !== undefined) {
          clauses.push('create_time >= ?');
          params.push(value.$gte);
        }
        if (value.$lte !== undefined) {
          clauses.push('create_time <= ?');
          params.push(value.$lte);
        }
        if (value.$gt !== undefined) {
          clauses.push('create_time > ?');
          params.push(value.$gt);
        }
        if (value.$lt !== undefined) {
          clauses.push('create_time < ?');
          params.push(value.$lt);
        }
      } else if (typeof value === 'object' && value !== null) {
        // Xử lý các operator đặc biệt
        if (value.$exists !== undefined) {
          if (value.$exists) {
            clauses.push(`${key} IS NOT NULL`);
          } else {
            clauses.push(`${key} IS NULL`);
          }
        }
        if (value.$nin !== undefined && Array.isArray(value.$nin)) {
          const placeholders = value.$nin.map(() => '?').join(', ');
          clauses.push(`${key} NOT IN (${placeholders})`);
          params.push(...value.$nin);
        }
        if (value.$in !== undefined && Array.isArray(value.$in)) {
          const placeholders = value.$in.map(() => '?').join(', ');
          clauses.push(`${key} IN (${placeholders})`);
          params.push(...value.$in);
        }
      } else if (value !== null && value !== undefined) {
        clauses.push(`${key} = ?`);
        params.push(value);
      }
    }

    return {
      whereClause: clauses.length > 0 ? clauses.join(' AND ') : '1',
      params
    };
  }

  /**
   * Đếm số tài khoản theo điều kiện (User specific)
   * @param {string} tableName - Tên bảng
   * @param {Object} conditions - Điều kiện query
   */
  static async countDocuments(tableName, conditions = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      return await this.count(tableName, whereClause, params);
    } catch (error) {
      logger.error('MySQL2Service.countDocuments error:', error);
      throw error;
    }
  }

  /**
   * Đếm tổng số tài khoản (User specific)
   * @param {string} tableName - Tên bảng
   */
  static async estimatedDocumentCount(tableName) {
    try {
      return await this.countDocuments(tableName);
    } catch (error) {
      logger.error('MySQL2Service.estimatedDocumentCount error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách distinct values
   * @param {string} tableName - Tên bảng
   * @param {string} field - Field để lấy distinct
   * @param {Object} conditions - Điều kiện query
   */
  static async distinct(tableName, field, conditions = {}) {
    try {
      if (field === 'ip') {
        const { whereClause, params } = this.buildWhereClause(conditions);
        let sql = `SELECT DISTINCT ${field} FROM ${tableName}`;

        if (whereClause !== '1') {
          sql += ` WHERE ${whereClause}`;
        }

        // Thêm điều kiện để loại bỏ IP null hoặc rỗng
        if (whereClause === '1') {
          sql += ` WHERE ${field} IS NOT NULL AND ${field} != ''`;
        } else {
          sql += ` AND ${field} IS NOT NULL AND ${field} != ''`;
        }

        const rows = await this.query(sql, params);
        return rows.map(row => Utils.formatIP(row[field])).filter(ip => ip);
      }

      throw new Error(`Distinct field '${field}' is not supported`);
    } catch (error) {
      logger.error('MySQL2Service.distinct error:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin theo ID
   * @param {string} tableName - Tên bảng
   * @param {string} idField - Tên field ID
   * @param {number} id - Giá trị ID
   * @param {Function} transformFn - Function transform data
   */
  static async findById(tableName, idField, id, transformFn = null) {
    try {
      const sql = `SELECT * FROM ${tableName} WHERE ${idField} = ?`;
      const result = await this.getOne(sql, [id]);
      return transformFn ? transformFn(result) : result;
    } catch (error) {
      logger.error('MySQL2Service.findById error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách theo điều kiện
   * @param {string} tableName - Tên bảng
   * @param {Object} conditions - Điều kiện query
   * @param {Object} options - Tùy chọn (limit, offset, orderBy)
   * @param {Function} transformFn - Function transform data
   */
  static async find(tableName, conditions = {}, options = {}, transformFn = null) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      let sql = `SELECT * FROM ${tableName}`;

      if (whereClause !== '1') {
        sql += ` WHERE ${whereClause}`;
      }

      if (options.orderBy) {
        sql += ` ORDER BY ${options.orderBy}`;
      }

      if (options.limit) {
        sql += ` LIMIT ${options.limit}`;
        if (options.offset) {
          sql += ` OFFSET ${options.offset}`;
        }
      }

      const rows = await this.query(sql, params);
      return transformFn ? rows.map(row => transformFn(row)) : rows;
    } catch (error) {
      logger.error('MySQL2Service.find error:', error);
      throw error;
    }
  }

  // === QUERY HELPERS USING PATTERN ===

  /**
   * Tìm records active
   * @param {string} tableName - Tên bảng
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findActive(tableName, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.activeUsers().where, options, transformFn);
  }

  /**
   * Tìm records theo IP
   * @param {string} tableName - Tên bảng
   * @param {string} ip - IP address
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findByIP(tableName, ip, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.byIP(ip).where, options, transformFn);
  }

  /**
   * Tìm records trong khoảng thời gian
   * @param {string} tableName - Tên bảng
   * @param {number} startTime - Timestamp bắt đầu
   * @param {number} endTime - Timestamp kết thúc
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findByTimeRange(tableName, startTime, endTime, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.byTimeRange(startTime, endTime).where, options, transformFn);
  }

  /**
   * Tìm records hôm nay
   * @param {string} tableName - Tên bảng
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findToday(tableName, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.today().where, options, transformFn);
  }

  /**
   * Tìm records tháng này
   * @param {string} tableName - Tên bảng
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findThisMonth(tableName, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.thisMonth().where, options, transformFn);
  }

  /**
   * Tìm records với IP hợp lệ
   * @param {string} tableName - Tên bảng
   * @param {Object} options - Tùy chọn
   * @param {Function} transformFn - Function transform data
   */
  static async findWithValidIP(tableName, options = {}, transformFn = null) {
    return await this.find(tableName, Queries.withValidIP().where, options, transformFn);
  }

  /**
   * Đếm records active
   * @param {string} tableName - Tên bảng
   */
  static async countActive(tableName) {
    return await this.countDocuments(tableName, Queries.activeUsers().where);
  }

  /**
   * Đếm records hôm nay
   * @param {string} tableName - Tên bảng
   */
  static async countToday(tableName) {
    return await this.countDocuments(tableName, Queries.today().where);
  }

  /**
   * Đếm records tháng này
   * @param {string} tableName - Tên bảng
   */
  static async countThisMonth(tableName) {
    return await this.countDocuments(tableName, Queries.thisMonth().where);
  }
}

export default MySQL2Service;
